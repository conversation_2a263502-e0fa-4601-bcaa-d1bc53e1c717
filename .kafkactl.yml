# kafkactl configuration for Kong Native Event Proxy (KNEP)
# This configuration provides contexts for connecting to team-a and team-b virtual clusters

contexts:
  # Team A context - connects via SNI routing with topic prefix 'a-'
  team-a:
    brokers:
      - bootstrap.team-a.127-0-0-1.sslip.io:9443
    tls:
      enabled: true
      insecure: true  # Set to false in production with proper CA
    sasl:
      enabled: false  # Anonymous authentication as configured in KNEP
    clientID: kafkactl-team-a
    requestTimeout: 10s
    retryBackoff: 100ms

  # Team B context - connects via SNI routing with topic prefix 'b-'
  team-b:
    brokers:
      - bootstrap.team-b.127-0-0-1.sslip.io:9443
    tls:
      enabled: true
      insecure: true  # Set to false in production with proper CA
    sasl:
      enabled: false  # Anonymous authentication as configured in KNEP
    clientID: kafkactl-team-b
    requestTimeout: 10s
    retryBackoff: 100ms

  # Direct Kafka cluster access (for admin operations)
  # Requires port-forwarding: kubectl port-forward svc/my-cluster-kafka-bootstrap 9092:9092 -n kafka
  kafka-direct:
    brokers:
      - localhost:9092
    tls:
      enabled: false
    sasl:
      enabled: false
    clientID: kafkactl-admin
    requestTimeout: 10s
    retryBackoff: 100ms

  # KNEP direct access (for testing without Kong Gateway)
  # Requires port-forwarding: kubectl port-forward svc/knep-gateway 9092:9092 -n knep
  knep-direct:
    brokers:
      - localhost:9092
    tls:
      enabled: false
    sasl:
      enabled: false
    clientID: kafkactl-knep
    requestTimeout: 10s
    retryBackoff: 100ms

# Default context
current-context: team-a

# Global settings
defaultPartitions: 3
defaultReplicationFactor: 3
