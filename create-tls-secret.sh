#!/bin/bash

# Create TLS secret using kubectl command
# This assumes you have your certificate and key files ready

# Method 1: Create from certificate and key files
kubectl create secret tls tls-secret \
  --cert=path/to/your/certificate.crt \
  --key=path/to/your/private.key \
  --namespace=knep

# Method 2: Create from existing certificate and key files with different names
# kubectl create secret tls tls-secret \
#   --cert=path/to/your/server.pem \
#   --key=path/to/your/server-key.pem \
#   --namespace=knep

# Method 3: If you want to create a self-signed certificate for testing
# openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
#   -keyout tls.key -out tls.crt \
#   -subj "/CN=knep-gateway.knep.svc.cluster.local"
# 
# kubectl create secret tls tls-secret \
#   --cert=tls.crt \
#   --key=tls.key \
#   --namespace=knep

echo "TLS secret 'tls-secret' created successfully in namespace 'knep'"
