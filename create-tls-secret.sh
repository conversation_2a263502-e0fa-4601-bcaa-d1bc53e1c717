#!/bin/bash

# Create wildcard TLS certificate covering all domains from cluster-issuer.yaml
# This creates a self-signed certificate that covers all the sslip.io domains

set -e

echo "Creating wildcard certificate for 127-0-0-1.sslip.io domains..."

# Create a temporary config file for the certificate with all Subject Alternative Names
cat > cert.conf <<EOF
[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C=US
ST=CA
L=San Francisco
O=Kong
OU=KNEP
CN=*.127-0-0-1.sslip.io

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = *.127-0-0-1.sslip.io
DNS.2 = 127-0-0-1.sslip.io
DNS.3 = *.team-a.127-0-0-1.sslip.io
DNS.4 = team-a.127-0-0-1.sslip.io
DNS.5 = *.team-b.127-0-0-1.sslip.io
DNS.6 = team-b.127-0-0-1.sslip.io
DNS.7 = bootstrap.team-a.127-0-0-1.sslip.io
DNS.8 = bootstrap.team-b.127-0-0-1.sslip.io
EOF

# Generate private key
openssl genrsa -out tls.key 2048

# Generate certificate signing request
openssl req -new -key tls.key -out tls.csr -config cert.conf

# Generate self-signed certificate valid for 1 year
openssl x509 -req -in tls.csr -signkey tls.key -out tls.crt -days 365 -extensions v3_req -extfile cert.conf

# Verify the certificate
echo "Certificate created. Verifying domains..."
openssl x509 -in tls.crt -text -noout | grep -A 10 "Subject Alternative Name"

# Create the Kubernetes TLS secret
kubectl create secret tls tls-secret \
  --cert=tls.crt \
  --key=tls.key \
  --namespace=knep \
  --dry-run=client -o yaml | kubectl apply -f -

# Clean up temporary files
rm -f cert.conf tls.csr

echo "✅ TLS secret 'tls-secret' created successfully in namespace 'knep'"
echo "📋 Certificate covers the following domains:"
echo "   - *.127-0-0-1.sslip.io"
echo "   - 127-0-0-1.sslip.io"
echo "   - *.team-a.127-0-0-1.sslip.io"
echo "   - team-a.127-0-0-1.sslip.io"
echo "   - *.team-b.127-0-0-1.sslip.io"
echo "   - team-b.127-0-0-1.sslip.io"
echo "   - bootstrap.team-a.127-0-0-1.sslip.io"
echo "   - bootstrap.team-b.127-0-0-1.sslip.io"
echo ""
echo "🔑 Certificate files generated:"
echo "   - tls.crt (certificate)"
echo "   - tls.key (private key)"
