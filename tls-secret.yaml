apiVersion: v1
kind: Secret
metadata:
  name: tls-secret
  namespace: knep
type: kubernetes.io/tls
data:
  # Base64 encoded TLS certificate
  # Replace this with your actual base64-encoded certificate
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCi4uLiAoWW91ciBiYXNlNjQgZW5jb2RlZCBjZXJ0aWZpY2F0ZSBoZXJlKSAuLi4KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQ==
  
  # Base64 encoded TLS private key
  # Replace this with your actual base64-encoded private key
  tls.key: LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0tCi4uLiAoWW91ciBiYXNlNjQgZW5jb2RlZCBwcml2YXRlIGtleSBoZXJlKSAuLi4KLS0tLS1FTkQgUFJJVkFURSBLRVktLS0tLQ==
