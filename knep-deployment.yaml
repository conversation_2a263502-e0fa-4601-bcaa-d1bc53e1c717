apiVersion: v1
kind: Secret
metadata:
  name: konnect-env-secret
  namespace: knep
type: Opaque
stringData:
  KONNECT_API_HOSTNAME: us.api.konghq.com
  KONNECT_CONTROL_PLANE_ID: 34eba191-e9ad-4685-b151-74e321d43fa0
  KONNECT_API_TOKEN: kpat_HDrWgEPhfZMcugQe1zLJRTDeEd1pUFAKi33YcVqDO02hhuxeB
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: knep-gateway
  namespace: knep
  labels:
    app: knep-gateway
spec:
  replicas: 1
  selector:
    matchLabels:
      app: knep-gateway
  template:
    metadata:
      labels:
        app: knep-gateway
      annotations:
        k8s.grafana.com/scrape: "true"
        k8s.grafana.com/metrics.path: "/health/metrics"
        k8s.grafana.com/metrics.portNumber: "8080"
    spec:
      containers:
      - name: gateway
        image: kong/kong-native-event-proxy:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 9092
        - containerPort: 8080
        volumeMounts:
        - name: tls-secret
          mountPath: /var/tls
          readOnly: true
        envFrom:
          - secretRef:
              name: konnect-env-secret
        env:
        - name: KNEP__OBSERVABILITY__LOG_FLAGS
          value: "info,knep=debug"
        readinessProbe:
          httpGet:
            path: /health/probes/readiness
            port: 8080
        livenessProbe:
          httpGet:
            path: /health/probes/liveness
            port: 8080
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
      terminationGracePeriodSeconds: 30
      volumes:
      - name: tls-secret
        secret:
          secretName: knep-tls
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: knep-gateway
  name: knep-gateway
  namespace: knep
spec:
  ports:
  - name: main
    port: 9092
    protocol: TCP
    targetPort: 9092
  - name: health
    port: 8080
    protocol: TCP
    targetPort: 8080
  selector:
    app: knep-gateway
  type: ClusterIP
---