---
apiVersion: gateway.networking.k8s.io/v1
kind: GatewayClass
metadata:
  name: kong
  annotations:
    konghq.com/gatewayclass-unmanaged: 'true'
spec:
  controllerName: konghq.com/kic-gateway-controller
---
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: kong
  namespace: knep
spec:
  gatewayClassName: kong
  listeners:
  - name: proxy
    port: 80
    protocol: HTTP
    allowedRoutes:
      namespaces:
         from: All
  - name: stream9443
    port: 9443
    protocol: TLS
    hostname: "bootstrap.team-a.127-0-0-1.sslip.io"
    tls:
      mode: Passthrough
      certificateRefs:
        - kind: Secret
          name: knep-tls
    allowedRoutes:
      namespaces:
         from: All
---
apiVersion: gateway.networking.k8s.io/v1alpha2
kind: TLSRoute
metadata:
  name: tls-passthrough-route
  namespace: knep
spec:
  parentRefs:
    - name: kong
      namespace: knep
      sectionName: stream9443
  hostnames:
    - "bootstrap.team-a.127-0-0-1.sslip.io"  # Must match the SNI in TLS handshake
    # - "*.team-b.127-0-0-1.sslip.io"  # Must match the SNI in TLS handshake
  rules:
    - backendRefs:
        - name: knep-gateway
          kind: Service
          namespace: knep
          port: 9092
