---
apiVersion: gateway.networking.k8s.io/v1
kind: GatewayClass
metadata:
  name: kong
  annotations:
    konghq.com/gatewayclass-unmanaged: 'true'
spec:
  controllerName: konghq.com/kic-gateway-controller
---
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: kong
  namespace: knep
spec:
  gatewayClassName: kong
  listeners:
  - name: proxy
    port: 80
    protocol: HTTP
  - name: stream9443
    port: 9443
    protocol: TLS
    tls:
      mode: Passthrough
      certificateRefs:
        - name: tls-secret
---
apiVersion: gateway.networking.k8s.io/v1alpha2
kind: TLSRoute
metadata:
  name: tls-passthrough-route-team-a
  namespace: knep
spec:
  parentRefs:
    - name: kong
      sectionName: stream9443
  hostnames:
    - "bootstrap.team-a.127-0-0-1.sslip.io"  # Must match the SNI in TLS handshake
    - "broker-0.team-a.127-0-0-1.sslip.io"  # Must match the SNI in TLS handshake
    - "broker-1.team-a.127-0-0-1.sslip.io"  # Must match the SNI in TLS handshake
    - "broker-2.team-a.127-0-0-1.sslip.io"  # Must match the SNI in TLS handshake
  rules:
    - backendRefs:
        - name: knep-gateway
          kind: Service
          port: 9092
---
apiVersion: gateway.networking.k8s.io/v1alpha2
kind: TLSRoute
metadata:
  name: tls-passthrough-route-team-b
  namespace: knep
spec:
  parentRefs:
    - name: kong
      sectionName: stream9443
  hostnames:
    - "bootstrap.team-b.127-0-0-1.sslip.io"  # Must match the SNI in TLS handshake
    - "broker-0.team-b.127-0-0-1.sslip.io"  # Must match the SNI in TLS handshake
    - "broker-1.team-b.127-0-0-1.sslip.io"  # Must match the SNI in TLS handshake
    - "broker-2.team-b.127-0-0-1.sslip.io"  # Must match the SNI in TLS handshake
  rules:
    - backendRefs:
        - name: knep-gateway
          kind: Service
          port: 9092
