backend_clusters:
  - name: kafka-cluster
    bootstrap_servers:
      - my-cluster-kafka-bootstrap.kafka.svc:9092

listeners:
  sni:
    - cert:
        file:
          path: /var/tls/tls.crt
        type: file
      key:
        file:
          path: /var/tls/tls.key
        type: file
      listen_address: 0.0.0.0
      listen_port: 9092
      sni_suffix: .127-0-0-1.sslip.io

virtual_clusters:
  - name: team-a
    backend_cluster_name: kafka-cluster
    route_by:
      type: sni
    authentication:
      - type: anonymous
        mediation:
          type: anonymous
    rewrite_ids:
      type: prefix
    topic_rewrite:
      type: prefix
      prefix:
        value: a-

  - name: team-b
    backend_cluster_name: kafka-cluster
    route_by:
      type: sni
    authentication:
      - type: anonymous
        mediation:
          type: anonymous
    rewrite_ids:
      type: prefix
    topic_rewrite:
      type: prefix
      prefix:
        value: b-
